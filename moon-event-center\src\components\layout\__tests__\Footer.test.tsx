import { describe, it, expect } from 'vitest'
import { screen } from '@testing-library/react'
import { render } from '../../../test/utils/test-utils'
import Footer from '../Footer'

describe('Footer Component', () => {
  it('renders company information', () => {
    render(<Footer />)
    
    expect(screen.getByText('Moon Event Center')).toBeInTheDocument()
    expect(screen.getByText('Richardson, Texas')).toBeInTheDocument()
    expect(screen.getByText(/Creating unforgettable moments/)).toBeInTheDocument()
  })

  it('renders quick links section', () => {
    render(<Footer />)
    
    expect(screen.getByText('Quick Links')).toBeInTheDocument()
    
    const quickLinks = [
      'Home',
      'About Us', 
      'Gallery',
      'Services',
      'Contact'
    ]
    
    quickLinks.forEach(link => {
      expect(screen.getByRole('link', { name: link })).toBeInTheDocument()
    })
  })

  it('renders services section', () => {
    render(<Footer />)
    
    expect(screen.getByText('Our Services')).toBeInTheDocument()
    
    const services = [
      'Wedding Receptions',
      'Corporate Events',
      'Birthday Parties',
      'Quinceañeras',
      'Community Gatherings',
      'Private Parties'
    ]
    
    services.forEach(service => {
      expect(screen.getByText(service)).toBeInTheDocument()
    })
  })

  it('renders contact information', () => {
    render(<Footer />)
    
    expect(screen.getByText('Contact Info')).toBeInTheDocument()
    
    // Address
    expect(screen.getByText('123 Event Center Drive')).toBeInTheDocument()
    expect(screen.getByText('Richardson, TX 75080')).toBeInTheDocument()
    
    // Phone
    const phoneLink = screen.getByRole('link', { name: '(*************' })
    expect(phoneLink).toHaveAttribute('href', 'tel:+1234567890')
    
    // Email
    const emailLink = screen.getByRole('link', { name: '<EMAIL>' })
    expect(emailLink).toHaveAttribute('href', 'mailto:<EMAIL>')
    
    // Business hours
    expect(screen.getByText('Mon-Fri: 9:00 AM - 6:00 PM')).toBeInTheDocument()
    expect(screen.getByText('Sat-Sun: 10:00 AM - 4:00 PM')).toBeInTheDocument()
  })

  it('renders copyright with current year', () => {
    render(<Footer />)
    
    const currentYear = new Date().getFullYear()
    expect(screen.getByText(`© ${currentYear} Moon Event Center. All rights reserved.`)).toBeInTheDocument()
  })

  it('renders legal links', () => {
    render(<Footer />)
    
    const privacyLink = screen.getByRole('link', { name: 'Privacy Policy' })
    expect(privacyLink).toHaveAttribute('href', '/privacy')
    
    const termsLink = screen.getByRole('link', { name: 'Terms of Service' })
    expect(termsLink).toHaveAttribute('href', '/terms')
  })

  it('has proper link attributes for external links', () => {
    render(<Footer />)
    
    // All internal links should not have target="_blank"
    const internalLinks = screen.getAllByRole('link').filter(link => {
      const href = link.getAttribute('href')
      return href && (href.startsWith('/') || href.startsWith('#'))
    })
    
    internalLinks.forEach(link => {
      expect(link).not.toHaveAttribute('target', '_blank')
    })
  })

  it('renders with proper semantic structure', () => {
    render(<Footer />)
    
    const footer = screen.getByRole('contentinfo')
    expect(footer).toBeInTheDocument()
    
    // Check for proper heading hierarchy
    const mainHeadings = screen.getAllByRole('heading', { level: 3 })
    expect(mainHeadings.length).toBeGreaterThan(0)
    
    const subHeadings = screen.getAllByRole('heading', { level: 4 })
    expect(subHeadings.length).toBeGreaterThan(0)
  })

  it('has accessible contact links', () => {
    render(<Footer />)
    
    // Phone link should be accessible
    const phoneLink = screen.getByRole('link', { name: '(*************' })
    expect(phoneLink).toBeInTheDocument()
    
    // Email link should be accessible
    const emailLink = screen.getByRole('link', { name: '<EMAIL>' })
    expect(emailLink).toBeInTheDocument()
  })
})
