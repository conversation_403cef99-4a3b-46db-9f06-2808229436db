import React from 'react';
import { motion } from 'framer-motion';
import SEOHead from '../components/seo/SEOHead';

const AboutPage: React.FC = () => {
  const fadeInUp = {
    initial: { opacity: 0, y: 30 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.8 }
  };

  const staggerContainer = {
    animate: {
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  return (
    <div className="min-h-screen">
      <SEOHead
        title="About Moon Event Center - Premier Wedding Venue in Richardson, Texas"
        description="Learn about Moon Event Center's story, mission, and commitment to creating magical wedding and event experiences in Richardson, Texas. Discover our elegant venue and exceptional service."
        keywords={[
          'about Moon Event Center',
          'wedding venue Richardson TX',
          'event center story',
          'Richardson wedding venue',
          'Texas event space',
          'wedding venue history',
          'event planning Richardson'
        ]}
        url="https://mooneventcenter.com/about"
        type="website"
      />

      {/* Hero Section */}
      <section className="section-padding bg-moon-white">
        <div className="container-max">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-serif font-bold text-moon-navy mb-6">
              About Moon Event Center
            </h1>
            <p className="text-xl md:text-2xl text-moon-navy max-w-3xl mx-auto leading-relaxed">
              Where celestial elegance meets unforgettable moments
            </p>
          </motion.div>
        </div>
      </section>

      {/* Our Story Section */}
      <section className="section-padding bg-moon-white">
        <div className="container-max">
          <motion.div
            variants={staggerContainer}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            className="grid lg:grid-cols-2 gap-12 items-center"
          >
            <motion.div variants={fadeInUp}>
              <h2 className="text-3xl md:text-4xl font-serif font-bold text-moon-navy mb-6">
                Our Story
              </h2>
              <div className="space-y-6 text-lg text-moon-text leading-relaxed">
                <p>
                  The Moon Event Center was born from a vision to create a space where dreams come to life
                  under the gentle glow of celestial inspiration. Located in the vibrant heart of Richardson,
                  Texas, our venue combines modern elegance with timeless sophistication.
                </p>
                <p>
                  Since our founding, we have been dedicated to providing couples, families, and organizations
                  with a breathtaking backdrop for their most cherished moments. Our commitment to excellence
                  and attention to detail has made us a premier destination for events that matter.
                </p>
                <p>
                  Every corner of our venue tells a story of craftsmanship, beauty, and the belief that
                  every celebration deserves to be extraordinary.
                </p>
              </div>
            </motion.div>
            <motion.div variants={fadeInUp} className="relative">
              <div className="aspect-square rounded-lg overflow-hidden shadow-xl">
                <img
                  src="/assets/Images/Wedding Celebration Dancefloor.png"
                  alt="Moon Event Center Interior"
                  className="w-full h-full object-cover"
                />
              </div>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Mission & Values Section */}
      <section className="section-padding bg-moon-white">
        <div className="container-max">
          <motion.div
            variants={staggerContainer}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <motion.h2
              variants={fadeInUp}
              className="text-3xl md:text-4xl font-serif font-bold text-moon-navy mb-6"
            >
              Our Mission & Values
            </motion.h2>
            <motion.p
              variants={fadeInUp}
              className="text-xl text-moon-navy max-w-3xl mx-auto leading-relaxed"
            >
              We believe every event is a unique constellation of moments waiting to shine
            </motion.p>
          </motion.div>

          <motion.div
            variants={staggerContainer}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            className="grid md:grid-cols-3 gap-8"
          >
            <motion.div variants={fadeInUp} className="text-center p-6">
              <div className="w-16 h-16 bg-moon-gold rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl text-moon-white">✨</span>
              </div>
              <h3 className="text-xl font-semibold text-moon-navy mb-3">Excellence</h3>
              <p className="text-moon-navy">
                We strive for perfection in every detail, ensuring your event exceeds expectations.
              </p>
            </motion.div>

            <motion.div variants={fadeInUp} className="text-center p-6">
              <div className="w-16 h-16 bg-moon-gold rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl text-moon-white">🌙</span>
              </div>
              <h3 className="text-xl font-semibold text-moon-navy mb-3">Elegance</h3>
              <p className="text-moon-navy">
                Our sophisticated atmosphere creates the perfect backdrop for memorable celebrations.
              </p>
            </motion.div>

            <motion.div variants={fadeInUp} className="text-center p-6">
              <div className="w-16 h-16 bg-moon-gold rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl text-moon-white">💫</span>
              </div>
              <h3 className="text-xl font-semibold text-moon-navy mb-3">Experience</h3>
              <p className="text-moon-navy">
                We create unforgettable moments that will be treasured for a lifetime.
              </p>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Venue Highlights Section */}
      <section className="section-padding bg-moon-white">
        <div className="container-max">
          <motion.div
            variants={staggerContainer}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
          >
            <motion.div variants={fadeInUp} className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-serif font-bold text-moon-navy mb-6">
                Venue Highlights
              </h2>
              <p className="text-xl text-moon-navy max-w-3xl mx-auto leading-relaxed">
                Discover what makes Moon Event Center the perfect choice for your special occasion
              </p>
            </motion.div>

            <motion.div
              variants={staggerContainer}
              className="grid md:grid-cols-2 lg:grid-cols-3 gap-8"
            >
              <motion.div variants={fadeInUp} className="bg-moon-white p-6 rounded-lg shadow-lg">
                <h3 className="text-xl font-semibold text-moon-navy mb-3">Elegant Ballroom</h3>
                <p className="text-moon-navy mb-4">
                  Our grand ballroom features soaring ceilings, crystal chandeliers, and space for up to 300 guests.
                </p>
                <ul className="text-base text-moon-navy space-y-1">
                  <li>• Professional lighting system</li>
                  <li>• Built-in sound system</li>
                  <li>• Dance floor</li>
                </ul>
              </motion.div>

              <motion.div variants={fadeInUp} className="bg-moon-white p-6 rounded-lg shadow-lg">
                <h3 className="text-xl font-semibold text-moon-navy mb-3">Outdoor Gardens</h3>
                <p className="text-moon-navy mb-4">
                  Beautiful landscaped gardens perfect for ceremonies and cocktail receptions.
                </p>
                <ul className="text-base text-moon-navy space-y-1">
                  <li>• Covered pavilion</li>
                  <li>• Garden lighting</li>
                  <li>• Scenic photo opportunities</li>
                </ul>
              </motion.div>

              <motion.div variants={fadeInUp} className="bg-moon-white p-6 rounded-lg shadow-lg">
                <h3 className="text-xl font-semibold text-moon-navy mb-3">Bridal Suite</h3>
                <p className="text-moon-navy mb-4">
                  Luxurious preparation space with all the amenities for the perfect getting-ready experience.
                </p>
                <ul className="text-base text-moon-navy space-y-1">
                  <li>• Private bathroom</li>
                  <li>• Professional lighting</li>
                  <li>• Comfortable seating area</li>
                </ul>
              </motion.div>

              <motion.div variants={fadeInUp} className="bg-moon-white p-6 rounded-lg shadow-lg">
                <h3 className="text-xl font-semibold text-moon-navy mb-3">Catering Kitchen</h3>
                <p className="text-moon-navy mb-4">
                  State-of-the-art commercial kitchen for seamless food service and preparation.
                </p>
                <ul className="text-base text-moon-navy space-y-1">
                  <li>• Professional equipment</li>
                  <li>• Ample prep space</li>
                  <li>• Service areas</li>
                </ul>
              </motion.div>

              <motion.div variants={fadeInUp} className="bg-moon-white p-6 rounded-lg shadow-lg">
                <h3 className="text-xl font-semibold text-moon-navy mb-3">Parking & Access</h3>
                <p className="text-moon-navy mb-4">
                  Convenient location with ample parking and easy access for all guests.
                </p>
                <ul className="text-base text-moon-navy space-y-1">
                  <li>• 150+ parking spaces</li>
                  <li>• ADA compliant</li>
                  <li>• Valet service available</li>
                </ul>
              </motion.div>

              <motion.div variants={fadeInUp} className="bg-moon-white p-6 rounded-lg shadow-lg">
                <h3 className="text-xl font-semibold text-moon-navy mb-3">Event Coordination</h3>
                <p className="text-moon-navy mb-4">
                  Professional event coordination to ensure every detail is perfectly executed.
                </p>
                <ul className="text-base text-moon-navy space-y-1">
                  <li>• Dedicated coordinator</li>
                  <li>• Timeline management</li>
                  <li>• Vendor coordination</li>
                </ul>
              </motion.div>
            </motion.div>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default AboutPage;
