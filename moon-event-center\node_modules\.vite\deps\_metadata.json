{"hash": "0b3a5e81", "configHash": "041b023a", "lockfileHash": "374dd522", "browserHash": "2abac6eb", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "63ca3ec4", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "1cf3782f", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "2379af84", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "ef6aad03", "needsInterop": true}, "@heroicons/react/24/outline": {"src": "../../@heroicons/react/24/outline/esm/index.js", "file": "@heroicons_react_24_outline.js", "fileHash": "5aa6a2aa", "needsInterop": false}, "@heroicons/react/24/solid": {"src": "../../@heroicons/react/24/solid/esm/index.js", "file": "@heroicons_react_24_solid.js", "fileHash": "5e0e61f6", "needsInterop": false}, "framer-motion": {"src": "../../framer-motion/dist/es/index.mjs", "file": "framer-motion.js", "fileHash": "71b2305f", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "5cdc91f6", "needsInterop": true}, "react-helmet-async": {"src": "../../../../node_modules/react-helmet-async/lib/index.esm.js", "file": "react-helmet-async.js", "fileHash": "fab3a6e0", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.mjs", "file": "react-router-dom.js", "fileHash": "7e56dfd9", "needsInterop": false}}, "chunks": {"chunk-2ZET3HRN": {"file": "chunk-2ZET3HRN.js"}, "chunk-S3Z6QACX": {"file": "chunk-S3Z6QACX.js"}, "chunk-IYDKXRZQ": {"file": "chunk-IYDKXRZQ.js"}}}