import { describe, it, expect, vi } from 'vitest'
import { screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { render } from '../../test/utils/test-utils'
import ContactPage from '../ContactPage'
import { mockContactFormData } from '../../test/fixtures/contact-fixtures'

// Mock framer-motion
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    section: ({ children, ...props }: any) => <section {...props}>{children}</section>,
  },
}))

describe('ContactPage Component', () => {
  it('renders contact page with all sections', () => {
    render(<ContactPage />)

    // Check main heading
    expect(screen.getByRole('heading', { name: 'Contact Us' })).toBeInTheDocument()

    // Check contact information section
    expect(screen.getByText('Get in Touch')).toBeInTheDocument()
    expect(screen.getAllByText('Visit Our Venue')).toHaveLength(2) // Appears in both contact info and venue sections
    expect(screen.getByText('Call Us')).toBeInTheDocument()
    expect(screen.getByText('Email Us')).toBeInTheDocument()

    // Check contact form
    expect(screen.getByText('Send us a Message')).toBeInTheDocument()
  })

  it('renders contact form with all required fields', () => {
    render(<ContactPage />)
    
    // Check form fields
    expect(screen.getByLabelText(/first name/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/last name/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/email/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/phone/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/event type/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/preferred date/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/guest count/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/tell us about your event/i)).toBeInTheDocument()
    
    // Check submit button
    expect(screen.getByRole('button', { name: 'Send Message' })).toBeInTheDocument()
  })

  it('validates required fields', async () => {
    const user = userEvent.setup()
    render(<ContactPage />)
    
    const submitButton = screen.getByRole('button', { name: 'Send Message' })
    
    // Try to submit empty form
    await user.click(submitButton)
    
    // Check that form doesn't submit (required fields should prevent submission)
    expect(screen.getByRole('button', { name: 'Send Message' })).toBeInTheDocument()
  })

  it('handles form input changes', async () => {
    const user = userEvent.setup()
    render(<ContactPage />)
    
    const firstNameInput = screen.getByLabelText(/first name/i)
    const emailInput = screen.getByLabelText(/email/i)
    const messageInput = screen.getByLabelText(/tell us about your event/i)
    
    // Type in form fields
    await user.type(firstNameInput, 'John')
    await user.type(emailInput, '<EMAIL>')
    await user.type(messageInput, 'Test message')
    
    // Check values
    expect(firstNameInput).toHaveValue('John')
    expect(emailInput).toHaveValue('<EMAIL>')
    expect(messageInput).toHaveValue('Test message')
  })

  it('handles form submission successfully', async () => {
    const user = userEvent.setup()
    render(<ContactPage />)
    
    // Fill out form
    await user.type(screen.getByLabelText(/first name/i), mockContactFormData.name.split(' ')[0])
    await user.type(screen.getByLabelText(/last name/i), mockContactFormData.name.split(' ')[1])
    await user.type(screen.getByLabelText(/email/i), mockContactFormData.email)
    await user.type(screen.getByLabelText(/phone/i), mockContactFormData.phone)
    await user.selectOptions(screen.getByLabelText(/event type/i), mockContactFormData.eventType)
    await user.type(screen.getByLabelText(/preferred date/i), mockContactFormData.eventDate)
    await user.selectOptions(screen.getByLabelText(/guest count/i), mockContactFormData.guestCount)
    await user.type(screen.getByLabelText(/tell us about your event/i), mockContactFormData.message)
    
    // Submit form
    const submitButton = screen.getByRole('button', { name: 'Send Message' })
    await user.click(submitButton)
    
    // Check loading state
    expect(screen.getByRole('button', { name: 'Sending...' })).toBeInTheDocument()
    
    // Wait for success message
    await waitFor(() => {
      expect(screen.getByText(/thank you! your message has been sent successfully/i)).toBeInTheDocument()
    }, { timeout: 3000 })
  })

  it('renders contact information correctly', () => {
    render(<ContactPage />)

    // Check address - appears in multiple places so use getAllByText
    expect(screen.getAllByText(/123 Event Center Drive/)).toHaveLength(2)
    expect(screen.getAllByText(/Richardson, TX 75080/)).toHaveLength(2)
    
    // Check phone link
    const phoneLink = screen.getByRole('link', { name: '(*************' })
    expect(phoneLink).toHaveAttribute('href', 'tel:+1234567890')
    
    // Check email link
    const emailLink = screen.getByRole('link', { name: '<EMAIL>' })
    expect(emailLink).toHaveAttribute('href', 'mailto:<EMAIL>')
    
    // Check business hours
    expect(screen.getByText(/monday - friday: 9:00 am - 6:00 pm/i)).toBeInTheDocument()
    expect(screen.getByText(/saturday: 10:00 am - 4:00 pm/i)).toBeInTheDocument()
    expect(screen.getByText(/sunday: by appointment only/i)).toBeInTheDocument()
  })

  it('renders quick action buttons', () => {
    render(<ContactPage />)
    
    const tourButton = screen.getByRole('link', { name: 'Schedule a Tour' })
    expect(tourButton).toHaveAttribute('href', 'tel:+1234567890')
    
    const quoteButton = screen.getByRole('link', { name: 'Request Quote' })
    expect(quoteButton).toHaveAttribute('href', 'mailto:<EMAIL>?subject=Event Inquiry')
  })

  it('renders Google Maps iframe', () => {
    render(<ContactPage />)
    
    const mapIframe = screen.getByTitle('Moon Event Center Location')
    expect(mapIframe).toBeInTheDocument()
    expect(mapIframe).toHaveAttribute('src')
  })

  it('renders FAQ section', () => {
    render(<ContactPage />)
    
    expect(screen.getByText('Frequently Asked Questions')).toBeInTheDocument()
    
    // Check for some FAQ questions
    expect(screen.getByText(/how far in advance should i book/i)).toBeInTheDocument()
    expect(screen.getByText(/do you provide catering services/i)).toBeInTheDocument()
    expect(screen.getByText(/is there a minimum guest count/i)).toBeInTheDocument()
    expect(screen.getByText(/can i schedule a venue tour/i)).toBeInTheDocument()
  })

  it('has proper form accessibility', () => {
    render(<ContactPage />)

    // Check that all form inputs have proper labels and required attributes
    const firstNameInput = screen.getByRole('textbox', { name: /first name/i })
    expect(firstNameInput).toHaveAttribute('required')

    const lastNameInput = screen.getByRole('textbox', { name: /last name/i })
    expect(lastNameInput).toHaveAttribute('required')

    const emailInput = screen.getByRole('textbox', { name: /email/i })
    expect(emailInput).toHaveAttribute('required')

    const messageInput = screen.getByRole('textbox', { name: /tell us about your event/i })
    expect(messageInput).toHaveAttribute('required')

    const eventTypeSelect = screen.getByRole('combobox', { name: /event type/i })
    expect(eventTypeSelect).toHaveAttribute('required')
  })
})
