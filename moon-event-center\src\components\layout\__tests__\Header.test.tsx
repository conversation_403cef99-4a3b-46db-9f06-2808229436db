import { describe, it, expect, vi } from 'vitest'
import { screen, fireEvent, waitFor } from '@testing-library/react'
import { render } from '../../../test/utils/test-utils'
import Header from '../Header'

// Mock framer-motion to avoid animation issues in tests
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
  AnimatePresence: ({ children }: any) => <>{children}</>,
}))

describe('Header Component', () => {
  it('renders the header with logo and navigation', () => {
    render(<Header />)
    
    // Check for logo
    expect(screen.getByText('Moon Event Center')).toBeInTheDocument()
    expect(screen.getByText('Richardson, Texas')).toBeInTheDocument()
    
    // Check for navigation items
    expect(screen.getByRole('link', { name: 'Home' })).toBeInTheDocument()
    expect(screen.getByRole('link', { name: 'About' })).toBeInTheDocument()
    expect(screen.getByRole('link', { name: 'Gallery' })).toBeInTheDocument()
    expect(screen.getByRole('link', { name: 'Services' })).toBeInTheDocument()
    expect(screen.getByRole('link', { name: 'Testimonials' })).toBeInTheDocument()
    expect(screen.getByRole('link', { name: 'Contact' })).toBeInTheDocument()
  })

  it('renders CTA buttons', () => {
    render(<Header />)
    
    const bookTourButtons = screen.getAllByText('Book a Tour')
    const checkAvailabilityButtons = screen.getAllByText('Check Availability')
    
    expect(bookTourButtons.length).toBeGreaterThan(0)
    expect(checkAvailabilityButtons.length).toBeGreaterThan(0)
  })

  it('toggles mobile menu when hamburger button is clicked', async () => {
    render(<Header />)
    
    const menuButton = screen.getByRole('button', { name: /navigation menu/i })
    expect(menuButton).toBeInTheDocument()
    
    // Initially, mobile menu should not be visible
    expect(screen.queryByRole('menu')).not.toBeInTheDocument()
    
    // Click to open menu
    fireEvent.click(menuButton)
    
    await waitFor(() => {
      expect(screen.getByRole('menu')).toBeInTheDocument()
    })
    
    // Click to close menu
    fireEvent.click(menuButton)
    
    await waitFor(() => {
      expect(screen.queryByRole('menu')).not.toBeInTheDocument()
    })
  })

  it('closes mobile menu when navigation link is clicked', async () => {
    render(<Header />)
    
    const menuButton = screen.getByRole('button', { name: /navigation menu/i })
    fireEvent.click(menuButton)
    
    await waitFor(() => {
      expect(screen.getByRole('menu')).toBeInTheDocument()
    })
    
    // Click on a navigation link in mobile menu
    const mobileNavLinks = screen.getAllByRole('link', { name: 'Home' })
    const mobileHomeLink = mobileNavLinks.find(link => 
      link.closest('[role="menu"]')
    )
    
    if (mobileHomeLink) {
      fireEvent.click(mobileHomeLink)
      
      await waitFor(() => {
        expect(screen.queryByRole('menu')).not.toBeInTheDocument()
      })
    }
  })

  it('has proper accessibility attributes', () => {
    render(<Header />)
    
    // Check for proper ARIA labels
    expect(screen.getByRole('banner')).toBeInTheDocument()
    expect(screen.getByRole('navigation', { name: 'Main navigation' })).toBeInTheDocument()
    
    const menuButton = screen.getByRole('button', { name: /navigation menu/i })
    expect(menuButton).toHaveAttribute('aria-expanded', 'false')
    expect(menuButton).toHaveAttribute('aria-controls', 'mobile-menu')
  })

  it('updates aria-expanded when mobile menu is toggled', async () => {
    render(<Header />)
    
    const menuButton = screen.getByRole('button', { name: /navigation menu/i })
    
    // Initially closed
    expect(menuButton).toHaveAttribute('aria-expanded', 'false')
    
    // Open menu
    fireEvent.click(menuButton)
    
    await waitFor(() => {
      expect(menuButton).toHaveAttribute('aria-expanded', 'true')
    })
  })

  it('renders correct navigation structure', () => {
    render(<Header />)
    
    const expectedNavItems = [
      { name: 'Home', href: '/' },
      { name: 'About', href: '/about' },
      { name: 'Gallery', href: '/gallery' },
      { name: 'Services', href: '/services' },
      { name: 'Testimonials', href: '/testimonials' },
      { name: 'Contact', href: '/contact' },
    ]
    
    expectedNavItems.forEach(item => {
      const link = screen.getByRole('link', { name: item.name })
      expect(link).toHaveAttribute('href', item.href)
    })
  })
})
