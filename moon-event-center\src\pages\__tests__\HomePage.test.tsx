import { describe, it, expect } from 'vitest'
import { screen } from '@testing-library/react'
import { render } from '../../test/utils/test-utils'
import HomePage from '../HomePage'

// Mock video element
Object.defineProperty(HTMLMediaElement.prototype, 'muted', {
  writable: true,
  value: false,
})

describe('HomePage Component', () => {
  it('renders the hero section with video background', () => {
    render(<HomePage />)

    // Check for main heading
    expect(screen.getByRole('heading', { name: 'Moon Event Center' })).toBeInTheDocument()

    // Check for location
    expect(screen.getByText('Richardson, Texas')).toBeInTheDocument()

    // Check for tagline
    expect(screen.getByText(/where celestial elegance meets unforgettable celebrations/i)).toBeInTheDocument()

    // Check for video element (it has aria-hidden="true" so we need to query differently)
    const videos = document.querySelectorAll('video')
    expect(videos.length).toBeGreaterThan(0)
    const video = videos[0]
    expect(video).toHaveAttribute('autoplay')
    expect(video).toHaveAttribute('loop')
    expect(video).toHaveAttribute('playsinline')
  })

  it('renders hero CTA buttons', () => {
    render(<HomePage />)

    // Check for primary CTA
    expect(screen.getByRole('link', { name: 'Book a Tour' })).toBeInTheDocument()

    // Check for secondary CTA
    expect(screen.getByRole('link', { name: 'Check Availability' })).toBeInTheDocument()
  })

  it('renders features section', () => {
    render(<HomePage />)

    expect(screen.getByText('Perfect for Every Celebration')).toBeInTheDocument()

    // Check for feature items
    expect(screen.getByText('Weddings')).toBeInTheDocument()
    expect(screen.getByText('Corporate Events')).toBeInTheDocument()
    expect(screen.getByText('Special Occasions')).toBeInTheDocument()
  })

  it('renders welcome section', () => {
    render(<HomePage />)

    expect(screen.getByText('Welcome to Moon Event Center')).toBeInTheDocument()

    // Check for welcome content
    expect(screen.getByText(/nestled in the heart of richardson, texas/i)).toBeInTheDocument()
    expect(screen.getByText(/breathtaking space that exudes sophistication/i)).toBeInTheDocument()
    expect(screen.getByText(/range of services to ensure your special day/i)).toBeInTheDocument()
  })

  it('renders testimonials section', () => {
    render(<HomePage />)

    expect(screen.getByText('What Our Clients Say')).toBeInTheDocument()

    // Check for testimonial content that actually exists
    expect(screen.getByText(/moon event center made our wedding day absolutely magical/i)).toBeInTheDocument()
    expect(screen.getByText(/sarah & michael johnson/i)).toBeInTheDocument()
  })

  it('renders gallery preview section', () => {
    render(<HomePage />)

    // Check for gallery links (there are multiple "View Gallery" links)
    const galleryLinks = screen.getAllByRole('link', { name: 'View Gallery' })
    expect(galleryLinks.length).toBeGreaterThan(0)
  })

  it('renders contact CTA section', () => {
    render(<HomePage />)

    expect(screen.getByText('Ready to Plan Your Perfect Event?')).toBeInTheDocument()
    expect(screen.getByText(/let us help you create an unforgettable experience/i)).toBeInTheDocument()

    // Check for contact CTAs
    expect(screen.getByRole('link', { name: 'Schedule a Tour' })).toBeInTheDocument()
    expect(screen.getByRole('link', { name: 'View Packages' })).toBeInTheDocument()
  })

  it('has proper video accessibility attributes', () => {
    render(<HomePage />)

    // Video has aria-hidden="true" so we need to query it directly
    const videos = document.querySelectorAll('video')
    expect(videos.length).toBeGreaterThan(0)
    const video = videos[0]
    expect(video).toHaveAttribute('aria-hidden', 'true')

    // Check for captions track
    const track = video.querySelector('track')
    expect(track).toHaveAttribute('kind', 'captions')
    expect(track).toHaveAttribute('srclang', 'en')
    expect(track).toHaveAttribute('label', 'English')
  })

  it('renders with proper semantic structure', () => {
    render(<HomePage />)

    // Check for proper heading hierarchy
    const h1 = screen.getByRole('heading', { level: 1 })
    expect(h1).toHaveTextContent('Moon Event Center')

    const h2Elements = screen.getAllByRole('heading', { level: 2 })
    expect(h2Elements.length).toBeGreaterThan(0)
  })

  it('has accessible navigation links', () => {
    render(<HomePage />)
    
    // Check that all links have proper href attributes
    const links = screen.getAllByRole('link')
    links.forEach(link => {
      expect(link).toHaveAttribute('href')
    })
    
    // Check specific important links
    const bookTourLink = screen.getByRole('link', { name: 'Book a Tour' })
    expect(bookTourLink).toHaveAttribute('href', '/contact')

    const checkAvailabilityLink = screen.getByRole('link', { name: 'Check Availability' })
    expect(checkAvailabilityLink).toHaveAttribute('href', '/contact')
  })

  it('renders feature icons and descriptions', () => {
    render(<HomePage />)

    // Check for feature descriptions that actually exist in the component
    expect(screen.getByText(/nestled in the heart of richardson, texas/i)).toBeInTheDocument()
    expect(screen.getByText(/breathtaking space that exudes sophistication/i)).toBeInTheDocument()
    expect(screen.getByText(/range of services to ensure your special day/i)).toBeInTheDocument()
  })
})
