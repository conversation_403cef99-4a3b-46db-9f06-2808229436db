import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { <PERSON><PERSON><PERSON><PERSON>outer } from 'react-router-dom';
import { He<PERSON>et<PERSON>rovider } from 'react-helmet-async';
import { AuthProvider } from '../contexts/AuthContext';
import { BookingProvider } from '../contexts/BookingContext';
import ProfilePage from '../pages/ProfilePage';
import BookingsPage from '../pages/BookingsPage';
import SettingsPage from '../pages/SettingsPage';
import FavoritesPage from '../pages/FavoritesPage';
import StaffDashboard from '../pages/StaffDashboard';
import ProtectedRoute from '../components/auth/ProtectedRoute';

// Mock user data
const mockCustomerUser = {
  id: 'user-1',
  email: '<EMAIL>',
  firstName: 'John',
  lastName: 'Doe',
  role: 'customer' as const,
  phone: '555-0123',
  createdAt: '2024-01-01T00:00:00Z',
  lastLogin: '2024-01-15T10:00:00Z'
};

const mockStaffUser = {
  id: 'staff-1',
  email: '<EMAIL>',
  firstName: 'Jane',
  lastName: '<PERSON>',
  role: 'staff' as const,
  phone: '555-0456',
  createdAt: '2024-01-01T00:00:00Z',
  lastLogin: '2024-01-15T09:00:00Z'
};

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode; user?: any }> = ({ 
  children, 
  user = mockCustomerUser 
}) => {
  // Mock AuthContext
  const mockAuthContext = {
    user,
    isLoading: false,
    isAuthenticated: !!user,
    login: jest.fn(),
    register: jest.fn(),
    logout: jest.fn(),
    updateProfile: jest.fn().mockResolvedValue({ success: true }),
    resetPassword: jest.fn()
  };

  return (
    <HelmetProvider>
      <BrowserRouter>
        <AuthProvider value={mockAuthContext}>
          <BookingProvider>
            {children}
          </BookingProvider>
        </AuthProvider>
      </BrowserRouter>
    </HelmetProvider>
  );
};

describe('User Portal Pages', () => {
  beforeEach(() => {
    // Clear localStorage before each test
    localStorage.clear();
    
    // Mock console methods to avoid noise in tests
    jest.spyOn(console, 'log').mockImplementation(() => {});
    jest.spyOn(console, 'warn').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('ProfilePage', () => {
    it('renders user profile information correctly', async () => {
      render(
        <TestWrapper>
          <ProfilePage />
        </TestWrapper>
      );

      expect(screen.getByText('My Profile')).toBeInTheDocument();
      expect(screen.getByText('John')).toBeInTheDocument();
      expect(screen.getByText('Doe')).toBeInTheDocument();
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      expect(screen.getByText('555-0123')).toBeInTheDocument();
    });

    it('allows editing profile information', async () => {
      render(
        <TestWrapper>
          <ProfilePage />
        </TestWrapper>
      );

      // Click edit button
      const editButton = screen.getByText('Edit');
      fireEvent.click(editButton);

      // Check that form fields are now editable
      const firstNameInput = screen.getByDisplayValue('John');
      expect(firstNameInput).toBeInTheDocument();
      
      // Change the first name
      fireEvent.change(firstNameInput, { target: { value: 'Johnny' } });
      
      // Save changes
      const saveButton = screen.getByText('Save');
      fireEvent.click(saveButton);

      await waitFor(() => {
        expect(screen.getByText('Profile updated successfully!')).toBeInTheDocument();
      });
    });

    it('shows account summary information', () => {
      render(
        <TestWrapper>
          <ProfilePage />
        </TestWrapper>
      );

      expect(screen.getByText('Account Summary')).toBeInTheDocument();
      expect(screen.getByText('Customer')).toBeInTheDocument();
      expect(screen.getByText('Active')).toBeInTheDocument();
    });
  });

  describe('BookingsPage', () => {
    it('renders bookings page with stats', () => {
      render(
        <TestWrapper>
          <BookingsPage />
        </TestWrapper>
      );

      expect(screen.getByText('My Bookings')).toBeInTheDocument();
      expect(screen.getByText('Total Bookings')).toBeInTheDocument();
      expect(screen.getByText('Confirmed')).toBeInTheDocument();
      expect(screen.getByText('Total Spent')).toBeInTheDocument();
    });

    it('shows filter options', () => {
      render(
        <TestWrapper>
          <BookingsPage />
        </TestWrapper>
      );

      expect(screen.getByText('Filter Bookings')).toBeInTheDocument();
      expect(screen.getByText('Status')).toBeInTheDocument();
      expect(screen.getByText('Event Type')).toBeInTheDocument();
      expect(screen.getByText('Payment Status')).toBeInTheDocument();
    });

    it('displays new booking button', () => {
      render(
        <TestWrapper>
          <BookingsPage />
        </TestWrapper>
      );

      const newBookingButton = screen.getByText('New Booking');
      expect(newBookingButton).toBeInTheDocument();
    });
  });

  describe('SettingsPage', () => {
    it('renders settings page with notification preferences', () => {
      render(
        <TestWrapper>
          <SettingsPage />
        </TestWrapper>
      );

      expect(screen.getByText('Settings')).toBeInTheDocument();
      expect(screen.getByText('Notification Preferences')).toBeInTheDocument();
      expect(screen.getByText('Email Notifications')).toBeInTheDocument();
      expect(screen.getByText('SMS Notifications')).toBeInTheDocument();
    });

    it('shows privacy settings', () => {
      render(
        <TestWrapper>
          <SettingsPage />
        </TestWrapper>
      );

      expect(screen.getByText('Privacy Settings')).toBeInTheDocument();
      expect(screen.getByText('Profile Visibility')).toBeInTheDocument();
      expect(screen.getByText('Share Contact Information')).toBeInTheDocument();
    });

    it('displays communication preferences', () => {
      render(
        <TestWrapper>
          <SettingsPage />
        </TestWrapper>
      );

      expect(screen.getByText('Communication Preferences')).toBeInTheDocument();
      expect(screen.getByText('Preferred Contact Method')).toBeInTheDocument();
      expect(screen.getByText('Timezone')).toBeInTheDocument();
      expect(screen.getByText('Language')).toBeInTheDocument();
    });

    it('shows account actions', () => {
      render(
        <TestWrapper>
          <SettingsPage />
        </TestWrapper>
      );

      expect(screen.getByText('Account Actions')).toBeInTheDocument();
      expect(screen.getByText('Change Password')).toBeInTheDocument();
      expect(screen.getByText('Delete Account')).toBeInTheDocument();
    });
  });

  describe('FavoritesPage', () => {
    it('renders favorites page with filter tabs', () => {
      render(
        <TestWrapper>
          <FavoritesPage />
        </TestWrapper>
      );

      expect(screen.getByText('My Favorites')).toBeInTheDocument();
      expect(screen.getByText('All Items (0)')).toBeInTheDocument();
      expect(screen.getByText('Services (0)')).toBeInTheDocument();
      expect(screen.getByText('Packages (0)')).toBeInTheDocument();
      expect(screen.getByText('Gallery (0)')).toBeInTheDocument();
    });

    it('shows empty state when no favorites', () => {
      render(
        <TestWrapper>
          <FavoritesPage />
        </TestWrapper>
      );

      expect(screen.getByText('No favorites yet')).toBeInTheDocument();
      expect(screen.getByText('Browse Services')).toBeInTheDocument();
      expect(screen.getByText('View Gallery')).toBeInTheDocument();
    });

    it('displays how to add favorites tip', () => {
      render(
        <TestWrapper>
          <FavoritesPage />
        </TestWrapper>
      );

      expect(screen.getByText('How to Add Favorites')).toBeInTheDocument();
      expect(screen.getByText(/Look for the heart icon/)).toBeInTheDocument();
    });
  });

  describe('StaffDashboard', () => {
    it('renders staff dashboard for staff users', () => {
      render(
        <TestWrapper user={mockStaffUser}>
          <StaffDashboard />
        </TestWrapper>
      );

      expect(screen.getByText('Staff Dashboard')).toBeInTheDocument();
      expect(screen.getByText('Welcome back, Jane!')).toBeInTheDocument();
      expect(screen.getByText('Total Bookings')).toBeInTheDocument();
      expect(screen.getByText('Pending Approval')).toBeInTheDocument();
    });

    it('shows quick actions for staff', () => {
      render(
        <TestWrapper user={mockStaffUser}>
          <StaffDashboard />
        </TestWrapper>
      );

      expect(screen.getByText('Quick Actions')).toBeInTheDocument();
      expect(screen.getByText('View Calendar')).toBeInTheDocument();
      expect(screen.getByText('Manage Customers')).toBeInTheDocument();
      expect(screen.getByText('Update Gallery')).toBeInTheDocument();
      expect(screen.getByText('Messages')).toBeInTheDocument();
    });

    it('denies access to customer users', () => {
      render(
        <TestWrapper user={mockCustomerUser}>
          <StaffDashboard />
        </TestWrapper>
      );

      expect(screen.getByText('Access Denied')).toBeInTheDocument();
      expect(screen.getByText('This area is restricted to staff members only.')).toBeInTheDocument();
    });
  });

  describe('ProtectedRoute', () => {
    it('allows access for authorized users', () => {
      render(
        <TestWrapper>
          <ProtectedRoute allowedRoles={['customer']}>
            <div>Protected Content</div>
          </ProtectedRoute>
        </TestWrapper>
      );

      expect(screen.getByText('Protected Content')).toBeInTheDocument();
    });

    it('denies access for unauthorized users', () => {
      render(
        <TestWrapper user={mockCustomerUser}>
          <ProtectedRoute allowedRoles={['staff']}>
            <div>Staff Only Content</div>
          </ProtectedRoute>
        </TestWrapper>
      );

      expect(screen.getByText('Access Denied')).toBeInTheDocument();
      expect(screen.queryByText('Staff Only Content')).not.toBeInTheDocument();
    });

    it('shows loading state when user is loading', () => {
      const loadingAuthContext = {
        user: null,
        isLoading: true,
        isAuthenticated: false,
        login: jest.fn(),
        register: jest.fn(),
        logout: jest.fn(),
        updateProfile: jest.fn(),
        resetPassword: jest.fn()
      };

      render(
        <HelmetProvider>
          <BrowserRouter>
            <AuthProvider value={loadingAuthContext}>
              <BookingProvider>
                <ProtectedRoute allowedRoles={['customer']}>
                  <div>Protected Content</div>
                </ProtectedRoute>
              </BookingProvider>
            </AuthProvider>
          </BrowserRouter>
        </HelmetProvider>
      );

      expect(screen.getByText('Loading...')).toBeInTheDocument();
    });
  });
});

describe('Integration Tests', () => {
  it('user can navigate between protected pages', async () => {
    // This would require more complex routing setup
    // For now, we'll test that pages render without errors
    const pages = [ProfilePage, BookingsPage, SettingsPage, FavoritesPage];
    
    pages.forEach((PageComponent) => {
      const { unmount } = render(
        <TestWrapper>
          <PageComponent />
        </TestWrapper>
      );
      
      // If we get here without throwing, the page rendered successfully
      expect(true).toBe(true);
      unmount();
    });
  });

  it('booking context provides data to all pages', () => {
    // Test that BookingContext is available in all user pages
    render(
      <TestWrapper>
        <BookingsPage />
      </TestWrapper>
    );

    // The page should render without context errors
    expect(screen.getByText('My Bookings')).toBeInTheDocument();
  });
});
