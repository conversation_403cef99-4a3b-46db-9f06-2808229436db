import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import Button from '../components/ui/Button';
import TestimonialsSection from '../components/sections/TestimonialsSection';
import SEOHead from '../components/seo/SEOHead';

const HomePage: React.FC = () => {
  return (
    <div className="min-h-screen">
      <SEOHead
        title="Moon Event Center - Premier Wedding & Event Venue in Richardson, Texas"
        description="Moon Event Center offers elegant wedding and event spaces in Richardson, Texas. From intimate ceremonies to grand celebrations, create unforgettable moments in our sophisticated venue."
        keywords={[
          'wedding venue Richardson TX',
          'event center Richardson',
          'wedding reception venue',
          'corporate events Richardson',
          'quinceañera venue',
          'Dallas wedding venue',
          'Texas event space',
          'elegant venue Richardson'
        ]}
        url="https://mooneventcenter.com"
        type="website"
      />

      {/* Hero Section with Video Background */}
      <section className="relative min-h-screen h-screen flex items-center justify-center overflow-hidden">
        {/* Video Background */}
        <video
          autoPlay
          loop
          muted
          playsInline
          className="absolute inset-0 w-full h-full object-cover"
          aria-hidden="true"
        >
          <source src="/assets/Videos/Moon-drone3.mp4" type="video/mp4" />
          Your browser does not support the video tag.
        </video>
        
        {/* Overlay */}
        <div className="absolute inset-0 bg-moon-navy/60"></div>
        
        {/* Hero Content */}
        <div className="relative z-10 text-center text-moon-white container-max px-4 mobile-spacing">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 0.5 }}
          >
            <h1 className="text-3xl sm:text-4xl md:text-6xl lg:text-7xl font-serif font-bold mb-4 md:mb-6 leading-tight">
              Moon Event Center
            </h1>
            <p className="text-lg sm:text-xl md:text-2xl mb-3 md:mb-4 text-moon-silver">
              Richardson, Texas
            </p>
            <p className="text-base sm:text-lg md:text-xl mb-6 md:mb-8 max-w-3xl mx-auto leading-relaxed px-2">
              Where celestial elegance meets unforgettable celebrations.
              Create magical moments in our sophisticated venue designed for your most special occasions.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/contact">
                <Button variant="black" size="lg" className="w-full sm:w-auto">
                  Book a Tour
                </Button>
              </Link>
              <Link to="/contact">
                <Button variant="black" size="lg" className="w-full sm:w-auto">
                  Check Availability
                </Button>
              </Link>
            </div>
          </motion.div>
        </div>
        
        {/* Scroll Indicator */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 2, duration: 1 }}
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
        >
          <div className="w-6 h-10 border-2 border-moon-white rounded-full flex justify-center">
            <motion.div
              animate={{ y: [0, 12, 0] }}
              transition={{ duration: 2, repeat: Infinity }}
              className="w-1 h-3 bg-moon-white rounded-full mt-2"
            />
          </div>
        </motion.div>
      </section>

      {/* Welcome Section */}
      <section className="section-padding bg-moon-white">
        <div className="container-max">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center max-w-4xl mx-auto"
          >
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-serif font-bold text-moon-navy mb-6">
              Welcome to Moon Event Center
            </h2>
            <p className="text-lg md:text-xl text-moon-silver-dark leading-relaxed mb-8">
              Nestled in the heart of Richardson, Texas, our mission is to provide couples and event planners 
              with a breathtaking space that exudes sophistication and grace. Located in a serene setting, 
              our event center offers a range of services to ensure your special day is nothing short of spectacular.
            </p>
            <Link to="/about">
              <Button variant="outline">
                Learn More About Us
              </Button>
            </Link>
          </motion.div>
        </div>
      </section>

      {/* Featured Events Section */}
      <section className="section-padding bg-moon-white">
        <div className="container-max">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl md:text-4xl font-serif font-bold text-moon-navy mb-4">
              Perfect for Every Celebration
            </h2>
            <p className="text-lg text-moon-silver-dark max-w-2xl mx-auto">
              From intimate gatherings to grand celebrations, we create the perfect atmosphere for your special moments.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                title: 'Weddings',
                description: 'Create your dream wedding in our elegant ballroom with celestial ambiance.',
                image: '/assets/Images/Wedding Celebration Dancefloor.png'
              },
              {
                title: 'Corporate Events',
                description: 'Professional venues perfect for meetings, conferences, and corporate celebrations.',
                image: '/assets/Images/game day.png'
              },
              {
                title: 'Special Occasions',
                description: 'Quinceañeras, birthdays, anniversaries, and all of life\'s milestone moments.',
                image: '/assets/Images/Wedding Celebration Dancefloor.png'
              }
            ].map((event, index) => (
              <motion.div
                key={event.title}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                viewport={{ once: true }}
                className="bg-moon-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300"
              >
                <div className="h-48 bg-moon-silver/20 flex items-center justify-center">
                  <img 
                    src={event.image} 
                    alt={event.title}
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-serif font-semibold text-moon-navy mb-3">
                    {event.title}
                  </h3>
                  <p className="text-moon-navy mb-4">
                    {event.description}
                  </p>
                  <Link to="/gallery">
                    <Button variant="outline" size="sm">
                      View Gallery
                    </Button>
                  </Link>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <TestimonialsSection
        title="What Our Clients Say"
        subtitle="Real experiences from couples and event planners who chose Moon Event Center"
        displayMode="carousel"
        showControls={true}
        autoPlay={true}
        maxItems={3}
        className="bg-moon-white"
      />

      {/* CTA Section */}
      <section className="section-padding bg-moon-white text-moon-navy">
        <div className="container-max text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl md:text-4xl font-serif font-bold mb-6">
              Ready to Plan Your Perfect Event?
            </h2>
            <p className="text-lg md:text-xl mb-8 max-w-2xl mx-auto">
              Let us help you create an unforgettable experience. Contact us today to schedule a tour 
              and see how we can make your vision come to life.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/contact">
                <Button variant="black" size="lg" className="w-full sm:w-auto">
                  Schedule a Tour
                </Button>
              </Link>
              <Link to="/services">
                <Button variant="black" size="lg" className="w-full sm:w-auto">
                  View Packages
                </Button>
              </Link>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default HomePage;
